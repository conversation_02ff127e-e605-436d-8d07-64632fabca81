["unit_test/test_app.py::TestAppBlueprints::test_documents_blueprint_registered", "unit_test/test_app.py::TestAppBlueprints::test_projects_blueprint_registered", "unit_test/test_app.py::TestAppBlueprints::test_upload_blueprint_registered", "unit_test/test_app.py::TestAppConfiguration::test_config_class_attributes", "unit_test/test_app.py::TestAppConfiguration::test_config_values_are_correct_types", "unit_test/test_app.py::TestAppErrorHandling::test_app_handles_404_errors", "unit_test/test_app.py::TestAppErrorHandling::test_app_handles_405_errors", "unit_test/test_app.py::TestAppFactory::test_app_has_correct_config_values", "unit_test/test_app.py::TestAppFactory::test_app_instance_exists", "unit_test/test_app.py::TestAppFactory::test_create_app_loads_config", "unit_test/test_app.py::TestAppFactory::test_create_app_registers_blueprints", "unit_test/test_app.py::TestAppFactory::test_create_app_returns_flask_instance", "unit_test/test_app.py::TestAppLogging::test_logging_configured"]